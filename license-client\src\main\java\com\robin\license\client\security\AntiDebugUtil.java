package com.robin.license.client.security;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * JVM反调试工具
 * 检测和防止调试器附加、代码注入等攻击
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class AntiDebugUtil {
    
    private static final AtomicBoolean debugDetected = new AtomicBoolean(false);
    private static final AtomicBoolean monitoringActive = new AtomicBoolean(false);
    private static ScheduledExecutorService monitorExecutor;
    
    // 调试相关的JVM参数
    private static final String[] DEBUG_ARGUMENTS = {
        "-Xdebug",
        "-agentlib:jdwp",
        "-Xrunjdwp",
        "-javaagent",
        "-XX:+UnlockDiagnosticVMOptions",
        "-XX:+LogVMOutput",
        "-XX:+TraceClassLoading",
        "-XX:+TraceClassUnloading",
        "-verbose:class",
        "-Xbootclasspath"
    };
    
    // 可疑的系统属性
    private static final String[] SUSPICIOUS_PROPERTIES = {
        "java.compiler",
        "sun.jdwp.listenerAddress",
        "jdwp.address",
        "debug.port"
    };
    
    /**
     * 启动反调试监控
     */
    public static void startAntiDebugMonitoring() {
        if (monitoringActive.compareAndSet(false, true)) {
            log.info("启动反调试监控");
            
            // 立即执行一次检测
            performDebugDetection();
            
            // 启动定期监控
            monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "AntiDebugMonitor");
                t.setDaemon(true);
                return t;
            });
            
            // 每5秒检测一次
            monitorExecutor.scheduleAtFixedRate(
                AntiDebugUtil::performDebugDetection,
                5, 5, TimeUnit.SECONDS
            );
        }
    }
    
    /**
     * 停止反调试监控
     */
    public static void stopAntiDebugMonitoring() {
        if (monitoringActive.compareAndSet(true, false)) {
            log.info("停止反调试监控");
            if (monitorExecutor != null) {
                monitorExecutor.shutdown();
                monitorExecutor = null;
            }
        }
    }
    
    /**
     * 执行调试检测
     */
    private static void performDebugDetection() {
        try {
            boolean detected = false;
            
            // 1. 检测JVM启动参数
            if (checkJVMArguments()) {
                log.warn("检测到调试相关的JVM参数");
                detected = true;
            }
            
            // 2. 检测系统属性
            if (checkSystemProperties()) {
                log.warn("检测到可疑的系统属性");
                detected = true;
            }
            
            // 3. 检测运行时环境
            if (checkRuntimeEnvironment()) {
                log.warn("检测到可疑的运行时环境");
                detected = true;
            }
            
            // 4. 时间检测
            if (performTimingDetection()) {
                log.warn("检测到异常的执行时间，可能存在调试器");
                detected = true;
            }
            
            // 5. 线程检测
            if (checkSuspiciousThreads()) {
                log.warn("检测到可疑的线程");
                detected = true;
            }
            
            if (detected && !debugDetected.get()) {
                debugDetected.set(true);
                handleDebugDetection();
            }
            
        } catch (Exception e) {
            log.error("反调试检测过程中发生异常", e);
        }
    }
    
    /**
     * 检测JVM启动参数
     */
    private static boolean checkJVMArguments() {
        try {
            RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
            List<String> arguments = runtimeMXBean.getInputArguments();
            
            for (String arg : arguments) {
                String lowerArg = arg.toLowerCase();
                for (String debugArg : DEBUG_ARGUMENTS) {
                    if (lowerArg.contains(debugArg.toLowerCase())) {
                        log.debug("发现调试参数: {}", arg);
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.debug("检测JVM参数时发生异常", e);
        }
        
        return false;
    }
    
    /**
     * 检测系统属性
     */
    private static boolean checkSystemProperties() {
        try {
            for (String property : SUSPICIOUS_PROPERTIES) {
                String value = System.getProperty(property);
                if (value != null && !value.isEmpty()) {
                    log.debug("发现可疑系统属性: {} = {}", property, value);
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("检测系统属性时发生异常", e);
        }
        
        return false;
    }
    
    /**
     * 检测运行时环境
     */
    private static boolean checkRuntimeEnvironment() {
        try {
            // 检测是否在IDE中运行
            String classPath = System.getProperty("java.class.path", "");
            String[] ideIndicators = {"idea", "eclipse", "netbeans", "vscode"};
            
            for (String indicator : ideIndicators) {
                if (classPath.toLowerCase().contains(indicator)) {
                    log.debug("检测到IDE环境: {}", indicator);
                    return true;
                }
            }
            
            // 检测命令行
            String command = System.getProperty("sun.java.command", "");
            if (command.contains("RemoteTestRunner") || 
                command.contains("JUnitStarter") ||
                command.contains("TestNG")) {
                log.debug("检测到测试环境");
                return true;
            }
            
        } catch (Exception e) {
            log.debug("检测运行时环境时发生异常", e);
        }
        
        return false;
    }
    
    /**
     * 时间检测 - 调试时代码执行会变慢
     */
    private static boolean performTimingDetection() {
        try {
            long startTime = System.nanoTime();
            
            // 执行一些简单的计算
            double result = 0;
            for (int i = 0; i < 10000; i++) {
                result += Math.sqrt(i) * Math.sin(i);
            }
            
            long endTime = System.nanoTime();
            long duration = endTime - startTime;
            
            // 如果执行时间超过预期，可能在调试
            // 正常情况下应该在几毫秒内完成
            boolean suspicious = duration > 50_000_000; // 50ms
            
            if (suspicious) {
                log.debug("执行时间异常: {}ns, 计算结果: {}", duration, result);
            }
            
            return suspicious;
            
        } catch (Exception e) {
            log.debug("时间检测时发生异常", e);
            return false;
        }
    }
    
    /**
     * 检测可疑线程
     */
    private static boolean checkSuspiciousThreads() {
        try {
            Thread[] threads = new Thread[Thread.activeCount()];
            Thread.enumerate(threads);
            
            for (Thread thread : threads) {
                if (thread != null) {
                    String threadName = thread.getName().toLowerCase();
                    
                    // 检测调试相关的线程名
                    if (threadName.contains("jdwp") ||
                        threadName.contains("debug") ||
                        threadName.contains("agent") ||
                        threadName.contains("profiler")) {
                        
                        log.debug("发现可疑线程: {}", thread.getName());
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.debug("检测线程时发生异常", e);
        }
        
        return false;
    }
    
    /**
     * 处理调试检测
     */
    private static void handleDebugDetection() {
        log.error("检测到调试环境，应用将采取保护措施");
        
        // 可以采取的保护措施：
        // 1. 退出应用
        // 2. 禁用关键功能
        // 3. 发送警报
        // 4. 清除敏感数据
        
        // 这里采用温和的方式，只是记录日志
        // 在生产环境中可以采取更严格的措施
        
        try {
            // 清理一些敏感信息
            System.clearProperty("user.name");
            System.clearProperty("user.home");
            
            // 触发垃圾回收，清理内存
            System.gc();
            
        } catch (Exception e) {
            log.error("处理调试检测时发生异常", e);
        }
    }
    
    /**
     * 检查是否检测到调试器
     * 
     * @return 是否检测到调试器
     */
    public static boolean isDebugDetected() {
        return debugDetected.get();
    }
    
    /**
     * 重置调试检测状态
     */
    public static void resetDebugDetection() {
        debugDetected.set(false);
    }
    
    /**
     * 执行一次性调试检测
     * 
     * @return 是否检测到调试器
     */
    public static boolean performSingleDetection() {
        performDebugDetection();
        return debugDetected.get();
    }
}
