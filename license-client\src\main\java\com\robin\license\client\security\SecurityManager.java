package com.robin.license.client.security;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 安全管理器
 * 统一管理SDK的安全策略和防护机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class SecurityManager {
    
    private static final SecurityManager INSTANCE = new SecurityManager();
    
    private final ReflectionGuard reflectionGuard;
    private final IntegrityChecker integrityChecker;
    private final AtomicBoolean securityInitialized = new AtomicBoolean(false);
    private final AtomicBoolean strictMode = new AtomicBoolean(true);
    
    // 安全配置
    private volatile boolean enableReflectionGuard = true;
    private volatile boolean enableIntegrityCheck = true;
    private volatile boolean enableCallStackVerification = true;
    private volatile boolean enableAntiDebug = true;
    
    private SecurityManager() {
        this.reflectionGuard = ReflectionGuard.getInstance();
        this.integrityChecker = IntegrityChecker.getInstance();
    }
    
    public static SecurityManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 初始化安全管理器
     *
     * @param strictMode 是否启用严格模式
     */
    public void initialize(boolean strictMode) {
        if (securityInitialized.compareAndSet(false, true)) {
            this.strictMode.set(strictMode);
            log.info("初始化安全管理器，严格模式: {}", strictMode);

            // 启动反调试监控
            AntiDebugUtil.startAntiDebugMonitoring();

            // 执行初始化安全检查
            performInitialSecurityChecks();

            // 启动安全监控
            startSecurityMonitoring();

            // 生成干扰代码
            ObfuscationUtil.generateGarbageCode();

            log.info("安全管理器初始化完成");
        }
    }
    
    /**
     * 执行初始安全检查
     */
    private void performInitialSecurityChecks() {
        try {
            // 1. 完整性检查
            if (enableIntegrityCheck) {
                log.debug("执行完整性检查...");
                if (!integrityChecker.verifyIntegrity()) {
                    handleSecurityViolation("完整性检查失败");
                }
            }
            
            // 2. 反调试检查
            if (enableAntiDebug) {
                log.debug("执行反调试检查...");
                if (isDebuggerAttached() || AntiDebugUtil.performSingleDetection()) {
                    handleSecurityViolation("检测到调试器");
                }
            }
            
            // 3. 环境检查
            performEnvironmentCheck();
            
        } catch (Exception e) {
            log.error("安全检查过程中发生异常", e);
            if (strictMode.get()) {
                throw new SecurityException("安全检查失败", e);
            }
        }
    }
    
    /**
     * 启动安全监控
     */
    private void startSecurityMonitoring() {
        // 启动后台监控线程
        Thread monitorThread = new Thread(this::securityMonitorLoop, "SecurityMonitor");
        monitorThread.setDaemon(true);
        monitorThread.start();
    }
    
    /**
     * 安全监控循环
     */
    private void securityMonitorLoop() {
        while (securityInitialized.get()) {
            try {
                // 定期执行安全检查
                Thread.sleep(30000); // 30秒检查一次
                
                if (enableAntiDebug && isDebuggerAttached()) {
                    handleSecurityViolation("运行时检测到调试器");
                }
                
                // 检查JVM参数是否被篡改
                checkJvmArguments();
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("安全监控过程中发生异常", e);
            }
        }
    }
    
    /**
     * 检查是否有调试器附加
     * 
     * @return 是否检测到调试器
     */
    private boolean isDebuggerAttached() {
        try {
            // 方法1: 检查JVM调试参数
            String jvmArgs = System.getProperty("java.vm.info", "");
            if (jvmArgs.contains("debug") || jvmArgs.contains("jdwp")) {
                return true;
            }
            
            // 方法2: 检查管理Bean
            java.lang.management.RuntimeMXBean runtimeMXBean = 
                java.lang.management.ManagementFactory.getRuntimeMXBean();
            java.util.List<String> arguments = runtimeMXBean.getInputArguments();
            
            for (String arg : arguments) {
                if (arg.contains("-agentlib:jdwp") || 
                    arg.contains("-Xdebug") || 
                    arg.contains("-Xrunjdwp")) {
                    return true;
                }
            }
            
            // 方法3: 时间检测法
            long startTime = System.nanoTime();
            // 执行一些简单操作
            for (int i = 0; i < 1000; i++) {
                Math.random();
            }
            long endTime = System.nanoTime();
            
            // 如果执行时间异常长，可能有调试器
            return (endTime - startTime) > 10000000; // 10ms
            
        } catch (Exception e) {
            log.debug("调试器检测过程中发生异常", e);
            return false;
        }
    }
    
    /**
     * 检查JVM参数
     */
    private void checkJvmArguments() {
        try {
            java.lang.management.RuntimeMXBean runtimeMXBean = 
                java.lang.management.ManagementFactory.getRuntimeMXBean();
            java.util.List<String> arguments = runtimeMXBean.getInputArguments();
            
            for (String arg : arguments) {
                // 检查可疑的JVM参数
                if (arg.contains("-javaagent") && !arg.contains("lombok")) {
                    log.warn("检测到可疑的Java Agent: {}", arg);
                    if (strictMode.get()) {
                        handleSecurityViolation("检测到未授权的Java Agent");
                    }
                }
            }
        } catch (Exception e) {
            log.debug("JVM参数检查过程中发生异常", e);
        }
    }
    
    /**
     * 执行环境检查
     */
    private void performEnvironmentCheck() {
        try {
            // 检查是否在IDE环境中运行
            String classPath = System.getProperty("java.class.path", "");
            if (classPath.contains("idea") || classPath.contains("eclipse") || 
                classPath.contains("netbeans") || classPath.contains("vscode")) {
                log.warn("检测到IDE环境");
                if (strictMode.get()) {
                    // 在生产环境中可以选择阻止在IDE中运行
                    // handleSecurityViolation("不允许在IDE环境中运行");
                }
            }
            
            // 检查系统属性
            String userDir = System.getProperty("user.dir", "");
            if (userDir.contains("src") || userDir.contains("target") || userDir.contains("build")) {
                log.warn("检测到开发环境路径: {}", userDir);
            }
            
        } catch (Exception e) {
            log.debug("环境检查过程中发生异常", e);
        }
    }
    
    /**
     * 处理安全违规
     * 
     * @param reason 违规原因
     */
    private void handleSecurityViolation(String reason) {
        log.error("安全违规: {}", reason);
        
        if (strictMode.get()) {
            // 严格模式下直接退出
            log.error("严格模式下检测到安全违规，程序将退出");
            System.exit(-1);
        } else {
            // 宽松模式下记录警告
            log.warn("宽松模式下检测到安全违规，继续运行但功能受限");
        }
    }
    
    /**
     * 验证方法调用的安全性
     * 
     * @param methodName 方法名
     * @param className 类名
     */
    public void verifyMethodCall(String className, String methodName) {
        if (!securityInitialized.get()) {
            return;
        }
        
        try {
            // 1. 反射检查
            if (enableReflectionGuard && ReflectionGuard.isReflectionCall()) {
                Class<?> targetClass = Class.forName(className);
                reflectionGuard.checkReflectionAccess(targetClass, methodName);
            }
            
            // 2. 调用栈验证
            if (enableCallStackVerification) {
                if (!ReflectionGuard.verifyCallerPackage("com.robin.license")) {
                    log.warn("检测到来自外部包的方法调用: {}.{}", className, methodName);
                }
            }
            
            // 3. 运行时完整性检查
            if (enableIntegrityCheck) {
                integrityChecker.runtimeIntegrityCheck();
            }
            
        } catch (Exception e) {
            log.error("方法调用安全验证失败: {}.{}", className, methodName, e);
            if (strictMode.get()) {
                throw new SecurityException("方法调用安全验证失败", e);
            }
        }
    }
    
    /**
     * 关闭安全管理器
     */
    public void shutdown() {
        securityInitialized.set(false);

        // 停止反调试监控
        AntiDebugUtil.stopAntiDebugMonitoring();

        log.info("安全管理器已关闭");
    }
    
    // Getter和Setter方法
    public void setEnableReflectionGuard(boolean enable) {
        this.enableReflectionGuard = enable;
    }
    
    public void setEnableIntegrityCheck(boolean enable) {
        this.enableIntegrityCheck = enable;
    }
    
    public void setEnableCallStackVerification(boolean enable) {
        this.enableCallStackVerification = enable;
    }
    
    public void setEnableAntiDebug(boolean enable) {
        this.enableAntiDebug = enable;
    }
    
    public boolean isSecurityInitialized() {
        return securityInitialized.get();
    }
}
