package com.robin.license.client.security;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;
import java.util.Random;

/**
 * 代码混淆工具类
 * 提供字符串混淆、方法名混淆、控制流混淆等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class ObfuscationUtil {
    
    // 混淆种子，用于生成一致的混淆结果
    private static final long OBFUSCATION_SEED = 0x5F3759DF;
    
    // 字符映射表
    private static final char[] CHAR_MAP = {
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
        'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
        'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
    };
    
    /**
     * 混淆字符串 - 使用XOR和位移操作
     * 
     * @param input 输入字符串
     * @return 混淆后的字符串
     */
    public static String obfuscateString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        try {
            byte[] inputBytes = input.getBytes("UTF-8");
            byte[] obfuscated = new byte[inputBytes.length];
            
            Random random = new Random(OBFUSCATION_SEED);
            
            for (int i = 0; i < inputBytes.length; i++) {
                // XOR操作 + 位移
                int key = random.nextInt(256);
                obfuscated[i] = (byte) ((inputBytes[i] ^ key) + (i % 7));
            }
            
            return Base64.getEncoder().encodeToString(obfuscated);
        } catch (Exception e) {
            log.error("字符串混淆失败", e);
            return input;
        }
    }
    
    /**
     * 解混淆字符串
     * 
     * @param obfuscated 混淆后的字符串
     * @return 原始字符串
     */
    public static String deobfuscateString(String obfuscated) {
        if (obfuscated == null || obfuscated.isEmpty()) {
            return obfuscated;
        }
        
        try {
            byte[] obfuscatedBytes = Base64.getDecoder().decode(obfuscated);
            byte[] original = new byte[obfuscatedBytes.length];
            
            Random random = new Random(OBFUSCATION_SEED);
            
            for (int i = 0; i < obfuscatedBytes.length; i++) {
                // 反向操作
                int key = random.nextInt(256);
                original[i] = (byte) ((obfuscatedBytes[i] - (i % 7)) ^ key);
            }
            
            return new String(original, "UTF-8");
        } catch (Exception e) {
            log.error("字符串解混淆失败", e);
            return obfuscated;
        }
    }
    
    /**
     * 生成假的方法调用 - 干扰静态分析
     */
    public static void generateFakeCalls() {
        // 生成一些假的方法调用，增加分析难度
        try {
            String[] fakeClasses = {
                "java.lang.System", "java.util.Random", "java.io.File",
                "java.net.URL", "java.security.MessageDigest"
            };
            
            for (String className : fakeClasses) {
                Class<?> clazz = Class.forName(className);
                Method[] methods = clazz.getDeclaredMethods();
                
                // 随机选择一些方法进行"调用"（实际不执行）
                if (methods.length > 0) {
                    Method method = methods[new Random().nextInt(methods.length)];
                    // 这里只是获取方法信息，不实际调用
                    String methodName = method.getName();
                    log.debug("Fake method reference: {}", methodName);
                }
            }
        } catch (Exception e) {
            // 忽略异常，这只是干扰代码
        }
    }
    
    /**
     * 控制流混淆 - 使用无意义的条件判断
     * 
     * @param value 输入值
     * @return 处理后的值
     */
    public static int obfuscateControlFlow(int value) {
        // 添加一些无意义但复杂的控制流
        int result = value;
        
        if (System.currentTimeMillis() % 2 == 0) {
            result = result ^ 0x5A5A5A5A;
            result = result ^ 0x5A5A5A5A; // 抵消上面的操作
        }
        
        // 添加一些永远不会执行的代码分支
        if (Math.PI == 3.0) {
            result = -1; // 永远不会执行
        }
        
        // 使用复杂的数学运算
        long temp = (long) result * 1000000007L;
        temp = temp % 1000000007L;
        result = (int) temp;
        
        return result;
    }
    
    /**
     * 生成垃圾代码 - 增加逆向工程难度
     */
    public static void generateGarbageCode() {
        // 生成一些看起来有用但实际无用的代码
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            Random random = new Random();
            
            for (int i = 0; i < 10; i++) {
                byte[] randomBytes = new byte[32];
                random.nextBytes(randomBytes);
                
                byte[] hash = md.digest(randomBytes);
                String hashString = Base64.getEncoder().encodeToString(hash);
                
                // 这些计算结果不会被使用，只是为了增加分析难度
                if (hashString.length() > 40) {
                    // 永远不会执行的代码
                    System.setProperty("fake.property", hashString);
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 字符串分割混淆 - 将字符串分割成多个部分
     * 
     * @param input 输入字符串
     * @return 重组后的字符串
     */
    public static String splitStringObfuscation(String input) {
        if (input == null || input.length() < 4) {
            return input;
        }
        
        // 将字符串分割成多个部分
        int mid = input.length() / 2;
        String part1 = input.substring(0, mid);
        String part2 = input.substring(mid);
        
        // 添加一些无意义的操作
        StringBuilder sb = new StringBuilder();
        sb.append(part1);
        
        // 插入一些干扰字符然后删除
        sb.append("FAKE");
        sb.delete(sb.length() - 4, sb.length());
        
        sb.append(part2);
        
        return sb.toString();
    }
    
    /**
     * 数值混淆 - 对数值进行复杂的数学运算
     * 
     * @param value 原始数值
     * @return 混淆后的数值（实际值不变）
     */
    public static int obfuscateNumber(int value) {
        // 使用复杂的数学运算，但最终结果不变
        int temp = value;
        temp = temp * 17 + 23;
        temp = temp ^ 0xAAAA;
        temp = temp - 23;
        temp = temp / 17;
        temp = temp ^ 0xAAAA;
        
        return temp;
    }
    
    /**
     * 反调试检测 - 检测是否在调试环境中运行
     * 
     * @return 是否检测到调试器
     */
    public static boolean isDebuggerPresent() {
        try {
            // 检测调试端口
            String jdwpAddress = System.getProperty("java.compiler");
            if (jdwpAddress != null && jdwpAddress.toLowerCase().contains("jdwp")) {
                return true;
            }
            
            // 检测JVM参数
            String[] debugArgs = {"-Xdebug", "-agentlib:jdwp", "-Xrunjdwp"};
            String jvmArgs = System.getProperty("sun.java.command", "");
            
            for (String arg : debugArgs) {
                if (jvmArgs.contains(arg)) {
                    return true;
                }
            }
            
            // 时间检测 - 调试时执行会变慢
            long startTime = System.nanoTime();
            for (int i = 0; i < 1000; i++) {
                Math.sqrt(i);
            }
            long endTime = System.nanoTime();
            
            // 如果执行时间异常长，可能在调试
            return (endTime - startTime) > 10000000; // 10ms
            
        } catch (Exception e) {
            return false;
        }
    }
}
